import { test, expect } from '@playwright/test';

test.describe('Dashboard Button Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the production site
    await page.goto('https://royalty.technology');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await page.waitForSelector('text=Welcome back', { timeout: 10000 });
  });

  test('Start New Project button should navigate to /start', async ({ page }) => {
    // Look for the Start New Project button
    const startButton = page.locator('button:has-text("Start New Project")');
    await expect(startButton).toBeVisible();
    
    // Click the button
    await startButton.click();
    
    // Check if navigation occurred (either URL change or content change)
    await page.waitForTimeout(2000);
    
    // Check for either URL change or content indicating we're on start page
    const currentUrl = page.url();
    const hasStartContent = await page.locator('text=Start').first().isVisible();
    
    expect(currentUrl.includes('/start') || hasStartContent).toBeTruthy();
  });

  test('Track Contribution button should navigate to /track', async ({ page }) => {
    // Look for the Track Contribution button
    const trackButton = page.locator('button:has-text("Track Contribution")');
    await expect(trackButton).toBeVisible();
    
    // Click the button
    await trackButton.click();
    
    // Check if navigation occurred
    await page.waitForTimeout(2000);
    
    const currentUrl = page.url();
    const hasTrackContent = await page.locator('text=Track').first().isVisible();
    
    expect(currentUrl.includes('/track') || hasTrackContent).toBeTruthy();
  });

  test('View Analytics button should navigate to /analytics', async ({ page }) => {
    // Look for the View Analytics button
    const analyticsButton = page.locator('button:has-text("View Analytics")');
    await expect(analyticsButton).toBeVisible();
    
    // Click the button
    await analyticsButton.click();
    
    // Check if navigation occurred
    await page.waitForTimeout(2000);
    
    const currentUrl = page.url();
    const hasAnalyticsContent = await page.locator('text=Analytics').first().isVisible();
    
    expect(currentUrl.includes('/analytics') || hasAnalyticsContent).toBeTruthy();
  });

  test('All dashboard buttons should be clickable', async ({ page }) => {
    // Check that all main dashboard buttons are present and clickable
    const buttons = [
      'button:has-text("Start New Project")',
      'button:has-text("Track Contribution")', 
      'button:has-text("View Analytics")'
    ];

    for (const buttonSelector of buttons) {
      const button = page.locator(buttonSelector);
      await expect(button).toBeVisible();
      await expect(button).toBeEnabled();
    }
  });
});
